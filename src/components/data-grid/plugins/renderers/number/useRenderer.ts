import { computed, ref } from 'vue'
import type {
  NumberRendererProps,
  NumberRendererConfig,
  FormattedNumberData,
  LocaleConfig,
  CurrencyConfig,
} from './types'

/**
 * 默认配置
 */
const DEFAULT_CONFIG: Required<
  Pick<NumberRendererConfig, 'numberType' | 'variant' | 'nullText'>
> = {
  numberType: 'decimal',
  variant: 'text',
  nullText: '--',
}

/**
 * 获取区域配置
 */
function getLocaleConfig(): LocaleConfig {
  // 优先从用户配置获取，如未设置则从浏览器locale获取
  const locale = navigator.language || 'zh-CN'

  // 根据locale确定默认货币
  const currencyMap: Record<string, string> = {
    'zh-CN': 'CNY',
    'zh-TW': 'TWD',
    'zh-HK': 'HKD',
    'en-US': 'USD',
    'ja-JP': 'JPY',
    'ko-KR': 'KRW',
    'en-GB': 'GBP',
    'de-DE': 'EUR',
    'fr-FR': 'EUR',
  }

  const currency = currencyMap[locale] || 'USD'

  return {
    locale,
    currency,
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 20,
    },
  }
}

/**
 * 格式化数值
 */
function formatNumber(
  value: number,
  config: NumberRendererConfig,
  localeConfig: LocaleConfig
): string {
  const formatConfig = config.format || {}
  const { numberType = 'decimal' } = config

  // 构建 Intl.NumberFormat 选项
  const options: Intl.NumberFormatOptions = {
    ...localeConfig.numberFormat,
    useGrouping: formatConfig.useGrouping !== false,
  }

  // 根据类型设置格式
  switch (numberType) {
    case 'integer':
      options.maximumFractionDigits = 0
      options.minimumFractionDigits = 0
      break

    case 'decimal':
      options.minimumFractionDigits = formatConfig.minimumFractionDigits ?? 0
      options.maximumFractionDigits = formatConfig.maximumFractionDigits ?? 4
      if (formatConfig.decimalPlaces !== undefined) {
        options.minimumFractionDigits = formatConfig.decimalPlaces
        options.maximumFractionDigits = formatConfig.decimalPlaces
      }
      break

    case 'currency':
      options.style = 'currency'
      options.currency = config.baseCurrency || localeConfig.currency
      options.minimumFractionDigits = formatConfig.minimumFractionDigits ?? 2
      options.maximumFractionDigits = formatConfig.maximumFractionDigits ?? 4
      break

    case 'percentage':
      options.style = 'percent'
      options.minimumFractionDigits = formatConfig.minimumFractionDigits ?? 1
      options.maximumFractionDigits = formatConfig.maximumFractionDigits ?? 2
      break

    case 'scientific':
      return value.toExponential(formatConfig.decimalPlaces ?? 2)

    case 'ratio':
      options.minimumFractionDigits = formatConfig.minimumFractionDigits ?? 2
      options.maximumFractionDigits = formatConfig.maximumFractionDigits ?? 4
      break
  }

  const formatter = new Intl.NumberFormat(localeConfig.locale, options)
  let formatted = formatter.format(value)

  // 添加前缀和后缀
  if (config.prefix) {
    formatted = config.prefix + formatted
  }
  if (config.suffix) {
    formatted = formatted + config.suffix
  }

  return formatted
}

/**
 * 货币换算
 */
function convertCurrency(
  value: number,
  currencyConfig: CurrencyConfig
): number {
  const exchangeRate = parseFloat(currencyConfig.exchange_rate)
  return value * exchangeRate
}

/**
 * 获取本位币符号
 */
function getBaseCurrencySymbol(baseCurrency: string = 'CNY'): string {
  const symbolMap: Record<string, string> = {
    CNY: '¥',
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    KRW: '₩',
    HKD: 'HK$',
    TWD: 'NT$',
  }
  return symbolMap[baseCurrency] || baseCurrency
}

/**
 * 格式化货币显示 - 修正版
 */
function formatCurrencyDisplay(
  foreignCurrencyValue: number, // 外币金额（数据库存储值）
  baseCurrencyValue: number, // 本位币金额（换算后）
  currencyConfig: CurrencyConfig,
  baseCurrency: string,
  showDual: boolean,
  dualMode: 'primary-secondary' | 'secondary-primary' = 'primary-secondary'
): { primary: string; secondary?: string } {
  const baseCurrencySymbol = getBaseCurrencySymbol(baseCurrency)

  // 本位币显示（主要）
  const baseCurrencyDisplay = baseCurrencyValue.toFixed(2)
  // 外币显示（辅助，带符号）
  const foreignCurrencyDisplay = `${currencyConfig.symbol} ${foreignCurrencyValue.toFixed(2)}`

  if (!showDual) {
    // 单显示时，显示本位币（带符号）
    return { primary: `${baseCurrencySymbol} ${baseCurrencyDisplay}` }
  }

  if (dualMode === 'primary-secondary') {
    return {
      primary: baseCurrencyDisplay, // 本位币金额（无符号）
      secondary: foreignCurrencyDisplay, // 外币金额（有符号，无括号）
    }
  } else {
    return {
      primary: foreignCurrencyDisplay, // 特殊情况：外币作为主显示
      secondary: `${baseCurrencySymbol} ${baseCurrencyDisplay}`, // 本位币作为辅显示（无括号）
    }
  }
}

/**
 * 计算进度值
 */
function calculateProgress(
  value: number,
  min: number = 0,
  max: number = 100
): number {
  if (value <= min) return 0
  if (value >= max) return 100
  return ((value - min) / (max - min)) * 100
}

/**
 * 数值渲染器 Hook
 */
export function useNumberRenderer(props: NumberRendererProps) {
  const localeConfig = ref(getLocaleConfig())

  // 合并配置
  const config = computed(() => ({
    ...DEFAULT_CONFIG,
    ...props.config,
  }))

  // 原始数值
  const rawValue = computed(() => {
    const value = props.value
    if (value === null || value === undefined || value === '') {
      return null
    }
    const num = typeof value === 'number' ? value : parseFloat(String(value))
    return isNaN(num) ? null : num
  })

  // 格式化数值数据
  const formattedData = computed((): FormattedNumberData => {
    const value = rawValue.value

    if (value === null) {
      return {
        rawValue: null,
        primaryDisplay: config.value.nullText || '--',
        isNegative: false,
        isZero: false,
        isNull: true,
      }
    }

    const isNegative = value < 0
    const isZero = value === 0

    // 处理货币换算
    if (config.value.numberType === 'currency' && config.value.currency) {
      console.log('config.value.currency', config.value.currency)
      const foreignCurrencyValue = value // 原始外币金额
      const baseCurrencyValue = convertCurrency(
        foreignCurrencyValue,
        config.value.currency
      ) // 本位币金额

      const currencyDisplay = formatCurrencyDisplay(
        foreignCurrencyValue, // 外币金额
        baseCurrencyValue, // 本位币金额
        config.value.currency,
        config.value.baseCurrency || 'CNY',
        config.value.showDualCurrency || false,
        config.value.dualCurrencyMode
      )

      return {
        rawValue: value,
        primaryDisplay: currencyDisplay.primary, // 本位币金额（无符号）
        secondaryDisplay: currencyDisplay.secondary, // 外币金额（有符号）
        isNegative,
        isZero,
        isNull: false,
        progressValue:
          config.value.variant === 'progress'
            ? calculateProgress(
                baseCurrencyValue, // 使用本位币金额计算进度
                config.value.progress?.min,
                config.value.progress?.max
              )
            : undefined,
      }
    }

    // 普通数值格式化
    const primaryDisplay = formatNumber(value, config.value, localeConfig.value)

    return {
      rawValue: value,
      primaryDisplay,
      isNegative,
      isZero,
      isNull: false,
      progressValue:
        config.value.variant === 'progress'
          ? calculateProgress(
              config.value.numberType === 'percentage' ? value * 100 : value,
              config.value.progress?.min,
              config.value.progress?.max
            )
          : undefined,
    }
  })

  // 样式类
  const numberClasses = computed(() => {
    const classes: string[] = []
    const data = formattedData.value
    const colors = config.value.colors

    // 支持 colored 和 text 变体应用颜色样式
    if (
      (config.value.variant === 'colored' || config.value.variant === 'text') &&
      colors &&
      !data.isNull
    ) {
      if (data.isNegative && colors.negativeColor) {
        classes.push(`text-${colors.negativeColor}`)
      } else if (data.isZero && colors.zeroColor) {
        classes.push(`text-${colors.zeroColor}`)
      } else if (!data.isNegative && !data.isZero && colors.positiveColor) {
        classes.push(`text-${colors.positiveColor}`)
      } else if (colors.defaultColor) {
        classes.push(`text-${colors.defaultColor}`)
      }
    }

    return classes.join(' ')
  })

  // Badge 样式类
  const badgeClasses = computed(() => {
    const classes = ['inline-flex', 'items-center']
    const data = formattedData.value

    if (!data.isNull) {
      if (data.isNegative) {
        classes.push(
          'bg-red-100',
          'text-red-800',
          'dark:bg-red-900',
          'dark:text-red-300'
        )
      } else if (data.isZero) {
        classes.push(
          'bg-gray-100',
          'text-gray-800',
          'dark:bg-gray-900',
          'dark:text-gray-300'
        )
      } else {
        classes.push(
          'bg-green-100',
          'text-green-800',
          'dark:bg-green-900',
          'dark:text-green-300'
        )
      }
    } else {
      classes.push(
        'bg-gray-100',
        'text-gray-500',
        'dark:bg-gray-900',
        'dark:text-gray-400'
      )
    }

    return classes.join(' ')
  })

  // 进度条配置
  const progressConfig = computed(() => {
    const progress = config.value.progress || {}
    return {
      value: formattedData.value.progressValue || 0,
      showText: progress.showText !== false,
      height: progress.height || '8px',
      color: progress.color || 'blue',
    }
  })

  return {
    config,
    rawValue,
    formattedData,
    numberClasses,
    badgeClasses,
    progressConfig,
    localeConfig,
  }
}
